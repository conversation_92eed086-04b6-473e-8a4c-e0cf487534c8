API_MODE=DEV|PROD
PORT=Port where the service will run
AWS_REGION=AWS region where infra is running
AWS_SOCKET_API_URL=The url of socket api
JWT_SECRET=The secret for jwt
DATABASE_URL=the string for database like "mysql://user:password@host:port/database"
FIREBASE_CONF=Firebase configs
PUSH_NOTIFICATION_PROVIDER=fcm|onesignal|pusher (default: fcm)
ONESIGNAL_APP_ID=OneSignal App ID (optional)
ONESIGNAL_API_KEY=OneSignal API Key (optional)
PUSHER_INSTANCE_ID=Pusher Instance ID (optional)
PUSHER_SECRET_KEY=Pusher Secret Key (optional)
EXTERNAL_API_TOKEN=Token from Backend API
EXTERNAL_API_URL=Backend api url
AWS_STATE_MACHINE_ARN=State machine arn from Aws
BUCKET_AVATAR_URL=Bucket S3 Url base
AWS_SQS_URL=Aws SQS url
USE_QUEUE=true|false
USE_SAVE_ROOM_ON_API=true|false
USE_SCHEDULER=true|false
REDIS_HOST=
REDIS_PORT=
REDIS_PASS=
REDIS_KEY_PREFIX=[use game-service]
REDIS_DB_ID=[use 0]
REDIS_USE_TLS=
SHOW_DEBUG_LOGS=true|false