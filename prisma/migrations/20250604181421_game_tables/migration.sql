-- CreateTable
CREATE TABLE `games` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `status` VARCHAR(191) NOT NULL,
    `round` INTEGER NOT NULL,
    `quiz_id` INTEGER NOT NULL,
    `game_mode` ENUM('singlePlayer', 'fourRandom', 'oneOnInvitedPlayers', 'spotMatch') NOT NULL,
    `max_users` INTEGER NOT NULL,
    `data` JSON NOT NULL,
    `state` ENUM('waitingPlayers', 'starting', 'answering', 'reviewing', 'endGameReview', 'closed', 'cancelled') NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `games_name_key`(`name`),
    INDEX `games_name_idx`(`name`),
    PRIMARY <PERSON>Y (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `game_users` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `connectionId` VARCHAR(191) NOT NULL,
    `roomName` VARCHAR(191) NULL,
    `roomOwner` BOOLEAN NOT NULL,
    `userId` BIGINT UNSIGNED NOT NULL,
    `quizId` INTEGER NOT NULL,
    `isConnected` BOOLEAN NOT NULL,
    `isBot` BOOLEAN NOT NULL DEFAULT false,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    INDEX `game_users_userId_idx`(`userId`),
    INDEX `game_users_roomName_idx`(`roomName`),
    INDEX `game_users_quizId_idx`(`quizId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `game_events` (
    `event_id` VARCHAR(191) NOT NULL,
    `is_executed` BOOLEAN NOT NULL DEFAULT false,
    `room_name` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`event_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `chat_messages` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `sender_identifier` VARCHAR(255) NOT NULL,
    `receiver_identifier` VARCHAR(255) NOT NULL,
    `content` TEXT NOT NULL,
    `created_at` TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
    `is_read` BOOLEAN NOT NULL DEFAULT false,

    INDEX `chat_messages_sender_identifier_idx`(`sender_identifier`),
    INDEX `chat_messages_receiver_identifier_idx`(`receiver_identifier`),
    INDEX `chat_messages_created_at_idx`(`created_at`),
    INDEX `chat_messages_is_read_idx`(`is_read`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `game_users` ADD CONSTRAINT `game_users_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `game_users` ADD CONSTRAINT `game_users_roomName_fkey` FOREIGN KEY (`roomName`) REFERENCES `games`(`name`) ON DELETE CASCADE ON UPDATE CASCADE;
