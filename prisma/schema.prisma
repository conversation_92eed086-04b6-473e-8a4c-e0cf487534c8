generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model accumulated_points {
  id         BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  user_id    BigInt    @db.UnsignedBigInt
  value      BigInt    @db.UnsignedBigInt
  created_at DateTime? @db.Timestamp(0)
  updated_at DateTime? @db.Timestamp(0)
  users      users     @relation(fields: [user_id], references: [id], onDelete: Cascade, map: "accumulated_points_user_id_foreign")

  @@index([user_id], map: "accumulated_points_user_id_foreign")
}

model answers {
  id                   BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  user_id              BigInt    @db.UnsignedBigInt
  room_id              BigInt    @db.UnsignedBigInt
  question_id          BigInt    @db.UnsignedBigInt
  value                String    @db.Text
  created_at           DateTime? @db.Timestamp(0)
  updated_at           DateTime? @db.Timestamp(0)
  questions_options_id String?   @db.Var<PERSON>har(255)
  questions            questions @relation(fields: [question_id], references: [id], onDelete: Cascade, map: "answers_question_id_foreign")
  rooms                rooms     @relation(fields: [room_id], references: [id], onDelete: Cascade, map: "answers_room_id_foreign")
  users                users     @relation(fields: [user_id], references: [id], onDelete: Cascade, map: "answers_user_id_foreign")
  points               points[]

  @@index([question_id], map: "answers_question_id_foreign")
  @@index([room_id], map: "answers_room_id_foreign")
  @@index([user_id], map: "answers_user_id_foreign")
}

model blacklists {
  id              BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  user_id         BigInt
  blocked_user_id BigInt
  created_at      DateTime? @db.Timestamp(0)
  updated_at      DateTime? @db.Timestamp(0)
}

model cadsysusers {
  id                String             @id @db.Char(36)
  name              String             @db.VarChar(255)
  email             String             @db.VarChar(255)
  username          String             @db.VarChar(255)
  password          String             @db.VarChar(255)
  created_at        DateTime           @default(now()) @db.Timestamp(0)
  updated_at        DateTime           @default(now()) @db.Timestamp(0)
  status            String             @default("active") @db.VarChar(255)
  situation         String             @default("pending") @db.VarChar(255)
  avatar            String             @default("active") @db.VarChar(255)
  cadsysuserstokens cadsysuserstokens?
}

model cadsysuserstokens {
  id          String      @id @db.Char(36)
  token       String      @db.Char(36)
  sysusers_id String      @unique(map: "REL_7d1d73aad7b6370027a3f3bef1") @db.Char(36)
  created_at  DateTime    @default(now()) @db.Timestamp(0)
  updated_at  DateTime    @default(now()) @db.Timestamp(0)
  cadsysusers cadsysusers @relation(fields: [sysusers_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "cadsysuserstokens_ibfk_1")
}

model cadusuariostokens {
  id             String   @id @db.Char(36)
  token          String   @db.Char(36)
  id_cadusuarios String   @db.VarChar(255)
  created_at     DateTime @default(now()) @db.Timestamp(0)
  updated_at     DateTime @default(now()) @db.Timestamp(0)
}

model failed_jobs {
  id         BigInt   @id @default(autoincrement()) @db.UnsignedBigInt
  uuid       String   @unique(map: "failed_jobs_uuid_unique") @db.VarChar(255)
  connection String   @db.Text
  queue      String   @db.Text
  payload    String   @db.LongText
  exception  String   @db.LongText
  failed_at  DateTime @default(now()) @db.Timestamp(0)
}

model friendly_invitations {
  id              BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  user_id         BigInt
  invited_user_id BigInt
  situation       String    @db.VarChar(255)
  created_at      DateTime? @db.Timestamp(0)
  updated_at      DateTime? @db.Timestamp(0)
  count           Int?      @default(1)
  lastInviteTime  DateTime? @default(now()) @db.Timestamp(0)
}

model friends {
  id                                  BigInt   @id @default(autoincrement())
  user_id                             BigInt   @db.UnsignedBigInt
  friend_user_id                      BigInt   @db.UnsignedBigInt
  situation                           String   @default("active") @db.VarChar(100)
  created_at                          DateTime @default(now()) @db.Timestamp(0)
  updated_at                          DateTime @default(now()) @db.Timestamp(0)
  users_friends_friend_user_idTousers users    @relation("friends_friend_user_idTousers", fields: [friend_user_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_83cfa95763744f6447a6da9ae40")
  users_friends_user_idTousers        users    @relation("friends_user_idTousers", fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_f2534e418d51fa6e5e8cdd4b480")

  @@index([friend_user_id], map: "FK_83cfa95763744f6447a6da9ae40")
  @@index([user_id], map: "FK_f2534e418d51fa6e5e8cdd4b480")
}

model informative_texts {
  id         BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  name       String    @db.VarChar(255)
  text       String    @db.Text
  created_at DateTime? @db.Timestamp(0)
  updated_at DateTime? @db.Timestamp(0)

  @@index([name], map: "informative_texts_name_index")
}

model levels {
  id         BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  name       String    @db.VarChar(255)
  value      Int       @db.UnsignedTinyInt
  created_at DateTime? @db.Timestamp(0)
  updated_at DateTime? @db.Timestamp(0)
  points     points[]

  @@index([name], map: "levels_name_index")
  @@index([value], map: "levels_value_index")
}

model match_history {
  id                                   BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  user_id                              BigInt    @db.UnsignedBigInt
  friend_id                            BigInt    @db.UnsignedBigInt
  match_tags                           String?   @db.VarChar(500)
  match_tags_obj                       Json?
  common_amount                        BigInt?   @default(0)
  distinct_amount                      BigInt?   @default(0)
  match_percent                        Float?    @default(0)
  created_at                           DateTime? @default(now()) @db.Timestamp(0)
  updated_at                           DateTime? @default(now()) @db.Timestamp(0)
  users_match_history_friend_idTousers users     @relation("match_history_friend_idTousers", fields: [friend_id], references: [id], onDelete: Cascade, map: "match_history_friend_id_foreign")
  users_match_history_user_idTousers   users     @relation("match_history_user_idTousers", fields: [user_id], references: [id], onDelete: Cascade, map: "match_history_user_id_foreign")

  @@index([friend_id], map: "match_history_friend_id_foreign")
  @@index([user_id], map: "match_history_user_id_foreign")
}

model migrations {
  id        Int    @id @default(autoincrement())
  timestamp BigInt
  name      String @db.VarChar(255)
}

model password_resets {
  id         BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  email      String    @db.VarChar(255)
  token      String    @db.VarChar(255)
  created_at DateTime? @db.Timestamp(0)

  @@index([email], map: "password_resets_email_index")
}

model personal_access_tokens {
  id             BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  tokenable_type String    @db.VarChar(255)
  tokenable_id   BigInt    @db.UnsignedBigInt
  name           String    @db.VarChar(255)
  token          String    @unique(map: "personal_access_tokens_token_unique") @db.VarChar(64)
  abilities      String?   @db.Text
  last_used_at   DateTime? @db.Timestamp(0)
  created_at     DateTime? @db.Timestamp(0)
  updated_at     DateTime? @db.Timestamp(0)

  @@index([tokenable_type, tokenable_id], map: "personal_access_tokens_tokenable_type_tokenable_id_index")
}

model point_types {
  id          BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  description String    @db.VarChar(255)
  value       String    @db.VarChar(255)
  created_at  DateTime? @db.Timestamp(0)
  updated_at  DateTime? @db.Timestamp(0)
  points      points[]

  @@index([description], map: "point_types_description_index")
  @@index([value], map: "point_types_value_index")
}

model points {
  id            BigInt      @id @default(autoincrement()) @db.UnsignedBigInt
  user_id       BigInt      @db.UnsignedBigInt
  room_id       BigInt      @db.UnsignedBigInt
  question_id   BigInt      @db.UnsignedBigInt
  level_id      BigInt      @db.UnsignedBigInt
  answer_id     BigInt      @db.UnsignedBigInt
  point_type_id BigInt      @db.UnsignedBigInt
  user_give_id  BigInt?
  created_at    DateTime?   @db.Timestamp(0)
  updated_at    DateTime?   @db.Timestamp(0)
  answers       answers     @relation(fields: [answer_id], references: [id], onDelete: Cascade, map: "points_answer_id_foreign")
  levels        levels      @relation(fields: [level_id], references: [id], onDelete: Cascade, map: "points_level_id_foreign")
  point_types   point_types @relation(fields: [point_type_id], references: [id], onDelete: Cascade, map: "points_point_type_id_foreign")
  questions     questions   @relation(fields: [question_id], references: [id], onDelete: Cascade, map: "points_question_id_foreign")
  rooms         rooms       @relation(fields: [room_id], references: [id], onDelete: Cascade, map: "points_room_id_foreign")
  users         users       @relation(fields: [user_id], references: [id], onDelete: Cascade, map: "points_user_id_foreign")

  @@index([answer_id], map: "points_answer_id_foreign")
  @@index([level_id], map: "points_level_id_foreign")
  @@index([point_type_id], map: "points_point_type_id_foreign")
  @@index([question_id], map: "points_question_id_foreign")
  @@index([room_id], map: "points_room_id_foreign")
  @@index([user_give_id], map: "points_user_give_id_index")
  @@index([user_id], map: "points_user_id_foreign")
}

model points_totals {
  id         BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  user_id    BigInt    @db.UnsignedBigInt
  tag_cod    String    @db.VarChar(128)
  total      BigInt    @default(0)
  created_at DateTime? @default(now()) @db.Timestamp(0)
  updated_at DateTime? @default(now()) @db.Timestamp(0)
  users      users     @relation(fields: [user_id], references: [id], onDelete: Cascade, map: "totals_user_id_foreign")

  @@index([tag_cod], map: "idx_tag_cod")
  @@index([user_id], map: "totals_user_id_foreign")
}

model questions {
  id                BigInt              @id @default(autoincrement()) @db.UnsignedBigInt
  description       String              @db.VarChar(255)
  is_actived        Boolean             @default(false)
  stage             Int                 @db.UnsignedTinyInt
  created_at        DateTime?           @db.Timestamp(0)
  updated_at        DateTime?           @db.Timestamp(0)
  type              String              @db.VarChar(255)
  answers           answers[]
  points            points[]
  questions_options questions_options[]

  @@index([description], map: "questions_description_index")
  @@index([is_actived], map: "questions_is_actived_index")
  @@index([stage], map: "questions_stage_index")
}

model questions_options {
  id                     BigInt                   @id @default(autoincrement())
  value                  String                   @db.VarChar(255)
  question_id            BigInt                   @db.UnsignedBigInt
  questions_options_tags String                   @db.VarChar(255)
  created_at             DateTime                 @default(now()) @db.Timestamp(0)
  updated_at             DateTime                 @default(now()) @db.Timestamp(0)
  status                 String                   @default("active") @db.VarChar(8)
  weighting              BigInt?                  @default(0)
  questions              questions                @relation(fields: [question_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "questions_options_ibfk_1")
  questionsOptionsTags   questions_options_tags[]

  @@index([question_id], map: "question_id")
}

model questions_options_tags {
  id                  BigInt            @id @default(autoincrement())
  question_options_id BigInt
  tags_id             String            @unique(map: "REL_e09a20e6e81735f2e9a832d638") @db.VarChar(255)
  created_at          DateTime          @default(now()) @db.Timestamp(0)
  updated_at          DateTime          @default(now()) @db.Timestamp(0)
  questions_options   questions_options @relation(fields: [question_options_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "questions_options_tags_ibfk_1")
  tags                tags              @relation(fields: [tags_id], references: [cod], onDelete: NoAction, onUpdate: NoAction, map: "questions_options_tags_ibfk_2")

  @@index([question_options_id], map: "question_options_id")
}

model reports {
  id               BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  user_id          BigInt
  reported_user_id BigInt
  reason           String?   @db.VarChar(500)
  created_at       DateTime? @db.Timestamp(0)
  updated_at       DateTime? @db.Timestamp(0)
}

model room_types {
  id         BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  type       String    @db.VarChar(255)
  amount     Int       @db.UnsignedTinyInt
  created_at DateTime? @db.Timestamp(0)
  updated_at DateTime? @db.Timestamp(0)

  @@index([type], map: "room_types_type_index")
}

model room_user {
  id          BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  user_id     BigInt    @db.UnsignedBigInt
  room_id     BigInt    @db.UnsignedBigInt
  is_canceled Boolean   @default(false)
  created_at  DateTime? @db.Timestamp(0)
  updated_at  DateTime? @db.Timestamp(0)
  rooms       rooms     @relation(fields: [room_id], references: [id], onDelete: Cascade, map: "room_user_room_id_foreign")
  users       users     @relation(fields: [user_id], references: [id], onDelete: Cascade, map: "room_user_user_id_foreign")

  @@index([is_canceled], map: "room_user_is_canceled_index")
  @@index([room_id], map: "room_user_room_id_foreign")
  @@index([user_id], map: "room_user_user_id_foreign")
}

model rooms {
  id                       BigInt      @id @default(autoincrement()) @db.UnsignedBigInt
  name                     String      @db.VarChar(255)
  how_many_people          Int         @db.UnsignedTinyInt
  is_bloqued               Boolean     @default(false)
  question_time_in_seconds Int?        @db.UnsignedInt
  response_time_in_seconds Int?        @db.UnsignedInt
  created_at               DateTime?   @db.Timestamp(0)
  updated_at               DateTime?   @db.Timestamp(0)
  answers                  answers[]
  points                   points[]
  room_user                room_user[]

  @@index([how_many_people], map: "rooms_how_many_people_index")
  @@index([is_bloqued], map: "rooms_is_bloqued_index")
  @@index([name], map: "rooms_name_index")
  @@index([question_time_in_seconds], map: "rooms_question_time_in_seconds_index")
  @@index([response_time_in_seconds], map: "rooms_response_time_in_seconds_index")
}

model spots {
  id            BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  location_code String    @db.Char(36)
  name          String    @db.VarChar(255)
  description   String?   @db.VarChar(500)
  address       String?   @db.VarChar(255)
  city          String?   @db.VarChar(100)
  state         String?   @db.VarChar(100)
  country       String?   @db.VarChar(100)
  image         String?   @db.VarChar(500)
  latitude      String?   @db.VarChar(50)
  longitude     String?   @db.VarChar(50)
  status        String    @default("active") @db.VarChar(10)
  created_at    DateTime? @default(now()) @db.Timestamp(0)
  updated_at    DateTime? @default(now()) @db.Timestamp(0)
  hour_opening  DateTime? @db.Time(0)
  hour_closing  DateTime? @db.Time(0)
}

model system_settings {
  id          BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  description String    @db.VarChar(255)
  value       String    @db.VarChar(255)
  is_active   Boolean
  created_at  DateTime? @db.Timestamp(0)
  updated_at  DateTime? @db.Timestamp(0)

  @@index([description], map: "system_settings_description_index")
  @@index([is_active], map: "system_settings_is_active_index")
  @@index([value], map: "system_settings_value_index")
}

model tags {
  cod                    String                  @id @db.VarChar(128)
  title                  String?                 @db.VarChar(255)
  description            String?                 @db.VarChar(1000)
  created_at             DateTime                @default(now()) @db.Timestamp(0)
  updated_at             DateTime                @default(now()) @db.Timestamp(0)
  color                  String?                 @db.VarChar(50)
  icon                   String?                 @db.VarChar(50)
  notification_message   String?                 @db.VarChar(255)
  notification_title     String?                 @db.VarChar(255)
  vibecheck_description  String?                 @db.VarChar(1000)
  questions_options_tags questions_options_tags?
  user_unlocked_tags     user_unlocked_tags[]
}

model terms {
  id          BigInt        @id @default(autoincrement())
  version     String        @db.VarChar(255)
  url         String?       @db.VarChar(255)
  content     String?       @db.VarChar(255)
  created_at  DateTime      @default(now()) @db.Timestamp(0)
  updated_at  DateTime      @default(now()) @db.Timestamp(0)
  terms_users terms_users[]
}

model terms_users {
  id          BigInt   @id @default(autoincrement())
  term_id     BigInt
  is_canceled Int      @default(0) @db.TinyInt
  created_at  DateTime @default(now()) @db.Timestamp(0)
  updated_at  DateTime @default(now()) @db.Timestamp(0)
  user_id     BigInt?  @db.UnsignedBigInt
  terms       terms    @relation(fields: [term_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "terms_id_fk")
  users       users?   @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "users_id_fk")

  @@index([term_id], map: "terms_id_fk")
  @@index([user_id], map: "users_id_fk")
}

model user_points {
  id         BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  user_id    BigInt    @db.UnsignedBigInt
  value      Int
  created_at DateTime? @db.Timestamp(0)
  updated_at DateTime? @db.Timestamp(0)
  users      users     @relation(fields: [user_id], references: [id], onDelete: Cascade, map: "user_points_user_id_foreign")

  @@index([user_id], map: "user_points_user_id_foreign")
}

model user_unlocked_tags {
  id         BigInt   @id @default(autoincrement())
  user_id    BigInt   @db.UnsignedBigInt
  tag_cod    String   @db.VarChar(255)
  created_at DateTime @default(now()) @db.Timestamp(0)
  updated_at DateTime @default(now()) @db.Timestamp(0)
  status     String   @default("active") @db.VarChar(255)
  tags       tags     @relation(fields: [tag_cod], references: [cod], onDelete: NoAction, onUpdate: NoAction, map: "usertagsunlockedtags_ibfk")
  users      users    @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "userunlockedtags_ibfk")

  @@index([tag_cod], map: "usertagsunlockedtags_ibfk")
  @@index([user_id], map: "userunlockedtags_ibfk")
}

model users {
  id                                           BigInt               @id @default(autoincrement()) @db.UnsignedBigInt
  uuid                                         String               @db.Char(36)
  name                                         String               @db.VarChar(255)
  email                                        String?              @unique(map: "users_email_unique") @db.VarChar(255)
  phone_number                                 String?              @unique(map: "users_phone_number_unique") @db.VarChar(30)
  identifier                                   String               @unique(map: "users_identifier_unique") @db.VarChar(255)
  avatar                                       String?              @db.VarChar(255)
  email_verified_at                            DateTime?            @db.Timestamp(0)
  password                                     String               @db.VarChar(255)
  remember_token                               String?              @db.VarChar(100)
  created_at                                   DateTime?            @db.Timestamp(0)
  updated_at                                   DateTime?            @db.Timestamp(0)
  google_id                                    String?              @db.VarChar(100)
  apple_id                                     String?              @db.VarChar(100)
  onesignal_id                                 String?              @db.VarChar(200)
  lastaccess_at                                DateTime?            @default(now()) @db.Timestamp(0)
  accumulated_points                           accumulated_points[]
  answers                                      answers[]
  friends_friends_friend_user_idTousers        friends[]            @relation("friends_friend_user_idTousers")
  friends_friends_user_idTousers               friends[]            @relation("friends_user_idTousers")
  match_history_match_history_friend_idTousers match_history[]      @relation("match_history_friend_idTousers")
  match_history_match_history_user_idTousers   match_history[]      @relation("match_history_user_idTousers")
  points                                       points[]
  points_totals                                points_totals[]
  room_user                                    room_user[]
  terms_users                                  terms_users[]
  user_points                                  user_points[]
  user_unlocked_tags                           user_unlocked_tags[]
  game_users                                   game_users[]

  @@index([email_verified_at], map: "users_email_verified_at_index")
  @@index([name], map: "users_name_index")
  @@index([uuid], map: "users_uuid_index")
}

model users_onesignal_tokens {
  id           Int      @id @default(autoincrement())
  uuid         String   @db.Char(36)
  user_id      String   @db.VarChar(255)
  onesignal_id Int
  created_at   DateTime @default(now()) @db.Timestamp(0)
  updated_at   DateTime @default(now()) @db.Timestamp(0)
}

// --> new

enum GameStateEnumerator {
  waitingPlayers
  starting
  answering
  reviewing
  endGameReview
  closed
  cancelled
}

enum GameModeEnumerator {
  singlePlayer
  fourRandom
  oneOnInvitedPlayers
  spotMatch
}

model games {
  id         Int                 @id @default(autoincrement())
  name       String              @unique
  status     String
  round      Int
  quiz_id    Int
  game_mode  GameModeEnumerator
  max_users  Int
  data       Json
  state      GameStateEnumerator
  createdAt  DateTime            @default(now())
  updatedAt  DateTime            @updatedAt
  game_users game_users[]

  @@index([name])
}

model game_users {
  id           Int      @id @default(autoincrement())
  connectionId String
  roomName     String?
  roomOwner    Boolean
  userId       BigInt   @db.UnsignedBigInt
  quizId       Int
  isConnected  Boolean
  isBot        Boolean  @default(false)
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt

  user users  @relation(fields: [userId], references: [id], onDelete: Cascade)
  game games? @relation(fields: [roomName], references: [name], onDelete: Cascade)

  @@index([userId])
  @@index([roomName])
  @@index([quizId])
}

model game_events {
  event_id    String   @id
  is_executed Boolean  @default(false)
  room_name   String
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  @@map("game_events")
}

model chat_messages {
  id                  Int      @id @default(autoincrement())
  sender_identifier   String   @db.VarChar(255)
  receiver_identifier String   @db.VarChar(255)
  content             String   @db.Text
  created_at          DateTime @default(now()) @db.Timestamp(0)
  is_read             Boolean  @default(false)

  @@index([sender_identifier])
  @@index([receiver_identifier])
  @@index([created_at])
  @@index([is_read])
}
