import { <PERSON><PERSON><PERSON><PERSON>, ICommand<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { UpsertCommand } from '../upsert.command';
import { PrismaService } from 'src/prisma/prisma.service';
import GameUserEntity from 'src/game-users/models/entities/game-user.entity';
import showDebugConsole from 'src/shared/utils/show-debug-console';
import bigIntReplacer from 'src/shared/utils/replacer';

@CommandHandler(UpsertCommand)
export class UpsertHandler implements ICommandHandler<UpsertCommand> {
  constructor(private readonly prismaService: PrismaService) {}

  async execute(command: UpsertCommand): Promise<GameUserEntity> {
    const { id, ...userData } = command.data;
    let gameUser: GameUserEntity;
    const orCondictions = <Record<string, any>[]>[
      { id },
      {
        userId: userData.userId,
      },
    ];
    if (userData.connectionId) {
      orCondictions.push({ connectionId: userData.connectionId });
    }
    showDebugConsole(
      `upserting game user: ${JSON.stringify(userData, bigIntReplacer)}`,
    );
    const userFound = await this.prismaService.game_users.findFirst({
      where: {
        OR: orCondictions,
      },
    });
    showDebugConsole(`userFound: ${JSON.stringify(userFound, bigIntReplacer)}`);
    try {
      if (userFound) {
        gameUser = await this.prismaService.game_users.update({
          where: { id: userFound.id },
          data: userData,
        });
      } else {
        gameUser = await this.prismaService.game_users.create({
          data: userData,
        });
      }
    } catch (err) {
      showDebugConsole(`error upserting game user: ${err.message}`);
    }

    showDebugConsole(`saved: ${JSON.stringify(userFound, bigIntReplacer)}`);

    return gameUser;
  }
}
