import { Command<PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { SendMessageToGameUsersCommand } from '../send-message-to-game-users.command';
import {
  ApiGatewayManagementApiClient,
  PostToConnectionCommand,
} from '@aws-sdk/client-apigatewaymanagementapi';
import MessageDTO from 'src/shared/dtos/message.dto';
import bigIntReplacer from 'src/shared/utils/replacer';
import showDebugConsole from 'src/shared/utils/show-debug-console';

@CommandHandler(SendMessageToGameUsersCommand)
export class SendMessageToGameUsersHandler
  implements ICommandHandler<SendMessageToGameUsersCommand>
{
  async execute(
    command: SendMessageToGameUsersCommand,
  ): Promise<MessageDTO<any>> {
    showDebugConsole(
      `Trying to send message to user ${command.data.connectionId}: ` +
        command.data.action,
    );
    const client = new ApiGatewayManagementApiClient({
      endpoint: process.env.AWS_SOCKET_API_URL,
      region: process.env.AWS_REGION,
    });
    if (!command.data.connectionId) return null;
    try {
      const requestParams = {
        ConnectionId: command.data.connectionId,
        Data: JSON.stringify(command.data, bigIntReplacer),
      };
      const postCommand = new PostToConnectionCommand(requestParams);
      const sendRes = await client.send(postCommand);

      showDebugConsole(
        `Message sent to ${command.data.connectionId}, res: ` +
          JSON.stringify(sendRes, bigIntReplacer),
      );
    } catch (err) {
      showDebugConsole(
        `error trying to send message to user ${command.data.connectionId}: ` +
          err,
      );
    }
    return command.data;
  }
}
