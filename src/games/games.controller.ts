import { Head<PERSON>, Body, Controller, Post } from '@nestjs/common';
import { GamesService } from './games.service';
import PlayerEnteringGameDTO from './models/dtos/player-entering-game.dto';
import GameDataDTO, { createGameDataDTO } from './models/dtos/game-data.dto';
import MessageDTO from '../shared/dtos/message.dto';
import ActionEnumerator from '../shared/enumerators/action.enumerator';
import ChangeGameStateDTO from './models/dtos/change-game-state.dto';
import { UserAnswerModel } from 'src/questions/models/answers.model';
import AddBotDTO from './models/dtos/add-bot.dto';

@Controller('games')
export class GamesController {
  constructor(private readonly gamesService: GamesService) {}

  getData(
    @Headers() headers: Record<string, string>,
    @Body() body: MessageDTO<any>,
  ): GameDataDTO<any> {
    const { authorization } = headers;
    const connectionId = headers['connectionid'] || headers['connectionId'];

    return createGameDataDTO({
      connectionId,
      token: authorization || body.authorization,
      data: body.data,
    });
  }

  @Post('/')
  async default(
    @Headers() headers: Record<string, string>,
    @Body() body: MessageDTO<any>,
  ) {
    switch (body.action) {
      case ActionEnumerator.initiate: {
        return await this.initiate(headers, body);
      }
      case ActionEnumerator.setPlayerAnswer: {
        return await this.sendAnswer(headers, body);
      }
      case ActionEnumerator.cancelGame: {
        return await this.cancelGame(headers, body);
      }
      case ActionEnumerator.startGame: {
        return await this.startGame(headers, body);
      }
      case ActionEnumerator.setPlayerPontuation: {
        return await this.sendPontuation(headers, body);
      }
    }
  }

  @Post('/initiate')
  async initiate(
    @Headers() headers: Record<string, string>,
    @Body() body: MessageDTO<PlayerEnteringGameDTO>,
  ) {
    const params = this.getData(headers, body);
    return await this.gamesService.initiate(params);
  }

  @Post('room-data')
  async getCurrentData(
    @Headers() headers: Record<string, string>,
    @Body() body: MessageDTO<PlayerEnteringGameDTO>,
  ) {
    const params = this.getData(headers, body);
    return this.gamesService.getCurrentData(params);
  }

  @Post('send-answer')
  async sendAnswer(
    @Headers() headers: Record<string, string>,
    @Body() body: MessageDTO<UserAnswerModel>,
  ) {
    const params = this.getData(headers, body);
    return this.gamesService.sendAnswer(params);
  }

  @Post('send-pontuation')
  async sendPontuation(
    @Headers() headers: Record<string, string>,
    @Body() body: MessageDTO<any>,
  ) {
    const params = this.getData(headers, body);
    return this.gamesService.sendPontuation(params);
  }

  @Post('start-game')
  async startGame(
    @Headers() headers: Record<string, string>,
    @Body() body: MessageDTO<any>,
  ) {
    const params = this.getData(headers, body);
    return this.gamesService.startGame(params);
  }

  @Post('change-state')
  async changeGameState(
    @Headers() headers: Record<string, string>,
    @Body() body: MessageDTO<ChangeGameStateDTO>,
  ) {
    const params = this.getData(headers, body);
    return this.gamesService.changeGameState(params);
  }

  @Post('add-bot')
  async addBot(
    @Headers() headers: Record<string, string>,
    @Body() body: MessageDTO<AddBotDTO>,
  ) {
    const params = this.getData(headers, body);
    return this.gamesService.addBot(params);
  }

  @Post('cancel')
  async cancelGame(
    @Headers() headers: Record<string, string>,
    @Body() body: MessageDTO<string>,
  ) {
    const params = this.getData(headers, body);
    return this.gamesService.cancelGame(params);
  }

  @Post('connect')
  async onConnect(
    @Headers() headers: Record<string, string>,
    @Body() body: MessageDTO<PlayerEnteringGameDTO>,
  ) {
    const params = this.getData(headers, body);
    return await this.gamesService.onConnect({
      ...params,
      data: headers['identifier'],
    });
  }

  @Post('disconnect')
  async onDisconnect(
    @Headers() headers: Record<string, string>,
    @Body() body: MessageDTO<PlayerEnteringGameDTO>,
  ) {
    const params = this.getData(headers, body);
    return this.gamesService.onDisconnect(params);
  }
}
