enum ChatActionEnumerator {
  // inbound
  sendMessage = 'sendMessage',
  getMessages = 'getMessages',
  getConversations = 'getConversations',
  markAsRead = 'markAsRead',
  getUnreadCount = 'getUnreadCount',
  getMessagesBetweenUsers = 'getMessagesBetweenUsers',

  // outbound
  conversationList = 'conversationList',
  messagesBetweenPlayers = 'messagesBetweenPlayers',
  messageList = 'messageList',
  messageSent = 'messageSent',
  messageReceived = 'messageReceived',
  messagesUpdated = 'messagesUpdated',
  conversationsUpdated = 'conversationsUpdated',
  countAsRead = 'countAsRead',
  unreadCountUpdated = 'unreadCountUpdated',
}

export default ChatActionEnumerator;
