import { Injectable, NotFoundException } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { PrismaService } from '../prisma/prisma.service';
import { PushNotificationService } from './services/push-notification.service';
import { SendChatMessageCommand } from './commands/send-chat-message.command';
import SendMessageDTO from './models/dtos/send-message.dto';
import GetMessagesDTO from './models/dtos/get-messages.dto';
import MarkAsReadDTO from './models/dtos/mark-as-read.dto';
import ChatMessageModel, {
  ChatConversationModel,
  ChatMessageResponse,
} from './models/chat-message.model';
import { ChatDataDTO } from './models/dtos/chat-message.dto';
import showDebugConsole from '../shared/utils/show-debug-console';
import { verify } from 'jsonwebtoken';
import { JwtPayload } from '../shared/types/jwt-payload.type';
import MessageDTO from 'src/shared/dtos/message.dto';
import ChatActionEnumerator from './models/enumerators/chat-action.enumerator';
import EraseHistoryDTO from './models/dtos/erase-history';

@Injectable()
export class ChatService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly pushNotificationService: PushNotificationService,
    private readonly commandBus: CommandBus,
  ) {}

  private getUserFromToken(token: string): any {
    try {
      const decoded = verify(token, process.env.JWT_SECRET) as JwtPayload;
      return decoded.user;
    } catch (error) {
      throw new NotFoundException('Invalid token');
    }
  }

  // Método principal para processar ações do chat
  async processAction(params: ChatDataDTO<any>): Promise<any> {
    const user = this.getUserFromToken(params.token);
    const userIdentifier = user.identifier;

    // Extrair os dados da ação (removendo a propriedade 'action')
    const { action, data } = params.data;

    switch (action) {
      case 'sendMessage':
        return await this.sendMessage(userIdentifier, data);
      case 'getMessages':
        return await this.getMessages(userIdentifier, data);
      case 'getConversations':
        return await this.getConversations(userIdentifier);
      case 'markAsRead':
        return await this.markAsRead(userIdentifier, data);
      case 'getUnreadCount':
        return await this.getUnreadCount(userIdentifier);
      case 'eraseHistory':
        return await this.eraseHistory(userIdentifier, data);
      case 'getMessagesBetweenUsers':
        return await this.getMessagesBetweenUsers(
          userIdentifier,
          data.otherUserIdentifier,
          data.page,
          data.limit,
        );
      default:
        throw new NotFoundException('Action not found');
    }
  }

  async sendMessage(
    senderIdentifier: string,
    messageData: SendMessageDTO,
  ): Promise<MessageDTO<ChatMessageModel>> {
    const [sender, receiver] = await Promise.all([
      this.prismaService.users.findFirst({
        where: { identifier: senderIdentifier },
        select: { id: true, identifier: true, name: true, avatar: true },
      }),
      this.prismaService.users.findFirst({
        where: { identifier: messageData.receiverIdentifier },
        select: { id: true, identifier: true, name: true, avatar: true },
      }),
    ]);

    if (!sender) {
      throw new NotFoundException('Sender not found');
    }

    if (!receiver) {
      throw new NotFoundException('Receiver not found');
    }

    // Criar a mensagem no banco
    const message = await this.prismaService.chat_messages.create({
      data: {
        sender_identifier: senderIdentifier,
        receiver_identifier: messageData.receiverIdentifier,
        content: messageData.content,
      },
    });

    const chatMessage: ChatMessageModel = {
      id: message.id,
      sender_identifier: senderIdentifier,
      receiver_identifier: messageData.receiverIdentifier,
      content: message.content,
      created_at: message.created_at,
      is_read: message.is_read,
      sender: {
        id: Number(sender.id),
        identifier: sender.identifier,
        name: sender.name,
        avatar: sender.avatar,
      },
      receiver: {
        id: Number(receiver.id),
        identifier: receiver.identifier,
        name: receiver.name,
        avatar: receiver.avatar,
      },
    };

    // Tentar enviar via socket primeiro
    const receiverConnectionId = await this.getUserConnectionId(
      Number(receiver.id),
    );
    let socketSent = false;

    if (receiverConnectionId) {
      try {
        socketSent = await this.commandBus.execute(
          new SendChatMessageCommand(chatMessage, receiverConnectionId),
        );
      } catch (error) {
        showDebugConsole(`Socket send failed: ${error.message}`);
      }
    }

    // Se não conseguiu enviar via socket, enviar push notification
    if (!socketSent) {
      showDebugConsole('Sending push notification as fallback');
      await this.pushNotificationService.sendChatNotification(
        Number(receiver.id),
        sender.name,
        messageData.content,
      );
    }

    return this.createMessage(ChatActionEnumerator.messageSent, chatMessage);
  }

  private createMessage<T>(
    action: ChatActionEnumerator,
    data: T,
  ): MessageDTO<T> {
    return {
      action: 'chat',
      authorization: '',
      connectionId: '',
      data: {
        action,
        authorization: '',
        connectionId: '',
        data,
      },
    } as unknown as MessageDTO<T>;
  }

  async getMessages(
    userIdentifier: string,
    params: GetMessagesDTO,
  ): Promise<MessageDTO<ChatMessageResponse>> {
    const page = params.page || 1;
    const limit = params.limit || 20;
    const skip = (page - 1) * limit;

    const [messages, total] = await Promise.all([
      this.prismaService.chat_messages.findMany({
        where: {
          OR: [
            { sender_identifier: userIdentifier },
            { receiver_identifier: userIdentifier },
          ],
        },
        orderBy: { created_at: 'desc' },
        skip,
        take: limit,
      }),
      this.prismaService.chat_messages.count({
        where: {
          OR: [
            { sender_identifier: userIdentifier },
            { receiver_identifier: userIdentifier },
          ],
        },
      }),
    ]);

    // Buscar dados dos usuários
    const userIdentifiers = Array.from(
      new Set([
        ...messages.map((m) => m.sender_identifier),
        ...messages.map((m) => m.receiver_identifier),
      ]),
    );

    const users = await this.prismaService.users.findMany({
      where: { identifier: { in: userIdentifiers } },
      select: { id: true, identifier: true, name: true, avatar: true },
    });

    const usersMap = new Map(users.map((user) => [user.identifier, user]));

    const chatMessages: ChatMessageModel[] = messages.map((message) => {
      const sender = usersMap.get(message.sender_identifier);
      const receiver = usersMap.get(message.receiver_identifier);

      return {
        id: message.id,
        sender_identifier: message.sender_identifier,
        receiver_identifier: message.receiver_identifier,
        content: message.content,
        created_at: message.created_at,
        is_read: message.is_read,
        sender: sender
          ? {
              id: Number(sender.id),
              identifier: sender.identifier,
              name: sender.name,
              avatar: sender.avatar,
            }
          : undefined,
        receiver: receiver
          ? {
              id: Number(receiver.id),
              identifier: receiver.identifier,
              name: receiver.name,
              avatar: receiver.avatar,
            }
          : undefined,
      };
    });

    return this.createMessage<ChatMessageResponse>(
      ChatActionEnumerator.messageList,
      {
        messages: chatMessages,
        total,
        page,
        limit,
        hasMore: skip + limit < total,
      },
    );
  }

  async getConversations(
    userIdentifier: string,
  ): Promise<MessageDTO<ChatConversationModel[]>> {
    // Buscar todas as conversas do usuário
    const conversations = await this.prismaService.$queryRaw<any[]>`
      SELECT
        CASE
          WHEN cm.sender_identifier = ${userIdentifier} THEN cm.receiver_identifier
          ELSE cm.sender_identifier
        END as userIdentifier,
        u.name as userName,
        u.avatar as userAvatar,
        cm.id as lastMessageId,
        cm.content as lastMessageContent,
        cm.created_at as lastMessageDate,
        cm.sender_identifier as lastMessageSenderIdentifier,
        cm.receiver_identifier as lastMessageReceiverIdentifier,
        cm.is_read as lastMessageIsRead,
        (
          SELECT COUNT(*)
          FROM chat_messages cm2
          WHERE cm2.sender_identifier != ${userIdentifier}
            AND cm2.receiver_identifier = ${userIdentifier}
            AND cm2.is_read = false
            AND (
              (cm2.sender_identifier = cm.sender_identifier AND cm2.receiver_identifier = cm.receiver_identifier) OR
              (cm2.sender_identifier = cm.receiver_identifier AND cm2.receiver_identifier = cm.sender_identifier)
            )
        ) as unreadCount
      FROM chat_messages cm
      INNER JOIN users u ON (
        CASE
          WHEN cm.sender_identifier = ${userIdentifier} THEN u.identifier = cm.receiver_identifier
          ELSE u.identifier = cm.sender_identifier
        END
      )
      WHERE cm.sender_identifier = ${userIdentifier} OR cm.receiver_identifier = ${userIdentifier}
      AND cm.id IN (
        SELECT MAX(cm3.id)
        FROM chat_messages cm3
        WHERE (cm3.sender_identifier = ${userIdentifier} OR cm3.receiver_identifier = ${userIdentifier})
        GROUP BY
          CASE
            WHEN cm3.sender_identifier = ${userIdentifier} THEN cm3.receiver_identifier
            ELSE cm3.sender_identifier
          END
      )
      ORDER BY cm.created_at DESC
    `;

    const resData = conversations.map((conv) => ({
      userIdentifier: conv.userIdentifier,
      userName: conv.userName,
      userAvatar: conv.userAvatar,
      lastMessage: {
        id: conv.lastMessageId,
        sender_identifier: conv.lastMessageSenderIdentifier,
        receiver_identifier: conv.lastMessageReceiverIdentifier,
        content: conv.lastMessageContent,
        created_at: conv.lastMessageDate,
        is_read: conv.lastMessageIsRead,
      },
      unreadCount: Number(conv.unreadCount),
    }));

    return this.createMessage(ChatActionEnumerator.conversationList, resData);
  }

  async markAsRead(
    userIdentifier: string,
    messageData: MarkAsReadDTO,
  ): Promise<MessageDTO<number>> {
    const result = await this.prismaService.chat_messages.updateMany({
      where: {
        id: { in: messageData.messageIds },
        receiver_identifier: userIdentifier,
        is_read: false,
      },
      data: {
        is_read: true,
      },
    });

    return this.createMessage(ChatActionEnumerator.countAsRead, result.count);
  }

  async eraseHistory(
    userIdentifier: string,
    messageData: EraseHistoryDTO,
  ): Promise<MessageDTO<ChatMessageResponse>> {
    await this.prismaService.chat_messages.deleteMany({
      where: {
        OR: [
          {
            sender_identifier: userIdentifier,
            receiver_identifier: messageData.friendIdentifier,
          },
          {
            sender_identifier: messageData.friendIdentifier,
            receiver_identifier: userIdentifier,
          },
        ],
      },
    });

    return this.createMessage<ChatMessageResponse>(
      ChatActionEnumerator.messagesBetweenPlayers,
      {
        userIdentifier1: userIdentifier,
        userIdentifier2: messageData.friendIdentifier,
        messages: [],
        total: 0,
        page: 0,
        limit: 0,
        hasMore: false,
      },
    );
  }

  async getUnreadCount(
    userIdentifier: string,
  ): Promise<MessageDTO<{ sender_identifier: string; count: number }[]>> {
    const unreadCountsBySender = await this.prismaService.chat_messages.groupBy(
      {
        by: ['sender_identifier'],
        where: {
          receiver_identifier: userIdentifier,
          is_read: false,
        },
        _count: {
          id: true,
        },
      },
    );

    const result = unreadCountsBySender.map((item) => ({
      sender_identifier: item.sender_identifier,
      count: item._count.id,
    }));

    return this.createMessage(ChatActionEnumerator.unreadCountUpdated, result);
  }

  private async getUserConnectionId(userId: number): Promise<string | null> {
    try {
      // Buscar o connectionId do usuário na tabela game_users
      const gameUser = await this.prismaService.game_users.findFirst({
        where: {
          userId: BigInt(userId),
          isConnected: true,
        },
        select: { connectionId: true },
        orderBy: { updated_at: 'desc' },
      });

      return gameUser?.connectionId || null;
    } catch (error) {
      showDebugConsole(`Error getting user connection ID: ${error.message}`);
      return null;
    }
  }

  async getMessagesBetweenUsers(
    userIdentifier1: string,
    userIdentifier2: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<MessageDTO<ChatMessageResponse>> {
    const skip = (page - 1) * limit;

    const [messages, total] = await Promise.all([
      this.prismaService.chat_messages.findMany({
        where: {
          OR: [
            {
              sender_identifier: userIdentifier1,
              receiver_identifier: userIdentifier2,
            },
            {
              sender_identifier: userIdentifier2,
              receiver_identifier: userIdentifier1,
            },
          ],
        },
        orderBy: { created_at: 'desc' },
        skip,
        take: limit,
      }),
      this.prismaService.chat_messages.count({
        where: {
          OR: [
            {
              sender_identifier: userIdentifier1,
              receiver_identifier: userIdentifier2,
            },
            {
              sender_identifier: userIdentifier2,
              receiver_identifier: userIdentifier1,
            },
          ],
        },
      }),
    ]);

    // Buscar dados dos usuários
    const users = await this.prismaService.users.findMany({
      where: { identifier: { in: [userIdentifier1, userIdentifier2] } },
      select: {
        id: true,
        identifier: true,
        name: true,
        avatar: true,
        lastaccess_at: true,
      },
    });

    const usersMap = new Map(users.map((user) => [user.identifier, user]));

    const chatMessages: ChatMessageModel[] = messages.map((message) => {
      const sender = usersMap.get(message.sender_identifier);
      const receiver = usersMap.get(message.receiver_identifier);

      return {
        id: message.id,
        sender_identifier: message.sender_identifier,
        receiver_identifier: message.receiver_identifier,
        content: message.content,
        created_at: message.created_at,
        is_read: message.is_read,
        sender: sender
          ? {
              id: Number(sender.id),
              identifier: sender.identifier,
              name: sender.name,
              avatar: sender.avatar,
              lastaccess_at: sender.lastaccess_at,
            }
          : undefined,
        receiver: receiver
          ? {
              id: Number(receiver.id),
              identifier: receiver.identifier,
              name: receiver.name,
              avatar: receiver.avatar,
              lastaccess_at: receiver.lastaccess_at,
            }
          : undefined,
      };
    });

    return this.createMessage<ChatMessageResponse>(
      ChatActionEnumerator.messagesBetweenPlayers,
      {
        userIdentifier1,
        userIdentifier2,
        messages: chatMessages,
        total,
        page,
        limit,
        hasMore: skip + limit < total,
      },
    );
  }
}
