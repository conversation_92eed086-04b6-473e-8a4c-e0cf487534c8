import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { NotificationsService } from '../../notifications/notifications.service';
import { NotificationPayload } from '../../notifications/interfaces/notification-payload.interface';
import showDebugConsole from '../../shared/utils/show-debug-console';

@Injectable()
export class PushNotificationService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly notificationsService: NotificationsService,
  ) {}

  async sendChatNotification(
    receiverId: number,
    senderName: string,
    message: string,
  ): Promise<boolean> {
    try {
      // Buscar o onesignal_id do usuário (token de push notification)
      const user = await this.prismaService.users.findUnique({
        where: { id: BigInt(receiverId) },
        select: { onesignal_id: true, name: true },
      });

      if (!user?.onesignal_id) {
        showDebugConsole(`User ${receiverId} has no push notification token`);
        return false;
      }

      // Criar payload da notificação
      const notificationPayload: NotificationPayload = {
        title: `Nova mensagem de ${senderName}`,
        body:
          message.length > 100 ? message.substring(0, 100) + '...' : message,
        data: {
          type: 'chat_message',
          senderId: receiverId.toString(),
          senderName: senderName,
        },
        sound: 'default',
        priority: 'high',
      };

      // Enviar notificação usando o serviço genérico
      const result = await this.notificationsService.sendNotification(
        user.onesignal_id,
        notificationPayload,
      );

      if (result.success) {
        showDebugConsole(
          `Push notification sent successfully: ${result.messageId}`,
        );
        return true;
      } else {
        showDebugConsole(`Error sending push notification: ${result.error}`);
        return false;
      }
    } catch (error) {
      showDebugConsole(`Error sending push notification: ${error.message}`);
      return false;
    }
  }

  async sendBulkChatNotifications(
    notifications: Array<{
      receiverId: number;
      senderName: string;
      message: string;
    }>,
  ): Promise<{ success: number; failed: number }> {
    try {
      // Buscar tokens de todos os usuários
      const userIds = notifications.map((n) => BigInt(n.receiverId));
      const users = await this.prismaService.users.findMany({
        where: { id: { in: userIds } },
        select: { id: true, onesignal_id: true },
      });

      // Criar mapa de userId -> token
      const userTokenMap = new Map<number, string>();
      users.forEach((user) => {
        if (user.onesignal_id) {
          userTokenMap.set(Number(user.id), user.onesignal_id);
        }
      });

      // Agrupar notificações por payload similar (mesmo sender e mensagem)
      const groupedNotifications = new Map<
        string,
        {
          tokens: string[];
          payload: NotificationPayload;
        }
      >();

      for (const notification of notifications) {
        const token = userTokenMap.get(notification.receiverId);
        if (!token) {
          showDebugConsole(
            `User ${notification.receiverId} has no push notification token`,
          );
          continue;
        }

        const key = `${notification.senderName}:${notification.message}`;
        if (!groupedNotifications.has(key)) {
          groupedNotifications.set(key, {
            tokens: [],
            payload: {
              title: `Nova mensagem de ${notification.senderName}`,
              body:
                notification.message.length > 100
                  ? notification.message.substring(0, 100) + '...'
                  : notification.message,
              data: {
                type: 'chat_message',
                senderName: notification.senderName,
              },
              sound: 'default',
              priority: 'high',
            },
          });
        }

        groupedNotifications.get(key)!.tokens.push(token);
      }

      // Enviar notificações em lote
      let totalSuccess = 0;
      let totalFailed = 0;

      for (const group of groupedNotifications.values()) {
        const result = await this.notificationsService.sendToMultipleTokens(
          group.tokens,
          group.payload,
        );

        totalSuccess += result.successCount;
        totalFailed += result.failureCount;
      }

      showDebugConsole(
        `Bulk notifications sent. Success: ${totalSuccess}, Failed: ${totalFailed}`,
      );
      return { success: totalSuccess, failed: totalFailed };
    } catch (error) {
      showDebugConsole(
        `Error sending bulk push notifications: ${error.message}`,
      );
      return { success: 0, failed: notifications.length };
    }
  }
}
