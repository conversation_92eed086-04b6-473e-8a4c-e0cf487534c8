import { <PERSON><PERSON><PERSON><PERSON>, ICommandHand<PERSON> } from '@nestjs/cqrs';
import { SendChatMessageCommand } from '../send-chat-message.command';
import {
  ApiGatewayManagementApiClient,
  PostToConnectionCommand,
} from '@aws-sdk/client-apigatewaymanagementapi';
import bigIntReplacer from 'src/shared/utils/replacer';
import showDebugConsole from '../../../shared/utils/show-debug-console';

@CommandHandler(SendChatMessageCommand)
export class SendChatMessageHandler
  implements ICommandHandler<SendChatMessageCommand>
{
  async execute(command: SendChatMessageCommand): Promise<boolean> {
    if (!command.receiverConnectionId) {
      showDebugConsole('No connection ID provided for chat message');
      return false;
    }

    try {
      showDebugConsole(
        `Sending chat message to connection: ${command.receiverConnectionId}`,
      );

      const client = new ApiGatewayManagementApiClient({
        endpoint: process.env.AWS_SOCKET_API_URL,
        region: process.env.AWS_REGION,
      });

      const messageData = {
        action: 'messageReceived',
        data: command.message,
      };

      const requestParams = {
        ConnectionId: command.receiverConnectionId,
        Data: JSON.stringify(
          {
            action: 'chat',
            authorization: '',
            connectionId: command.receiverConnectionId,
            data: messageData,
          },
          bigIntReplacer,
        ),
      };

      showDebugConsole(`Data: ${JSON.stringify(requestParams)}`);

      const postCommand = new PostToConnectionCommand(requestParams);

      await client.send(postCommand);
      showDebugConsole('Chat message sent successfully via socket');
      return true;
    } catch (error) {
      showDebugConsole(
        `Error sending chat message via socket: ${error.message}`,
      );
      return false;
    }
  }
}
