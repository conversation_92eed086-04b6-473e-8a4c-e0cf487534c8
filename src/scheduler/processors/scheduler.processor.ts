import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { queuesConst } from 'src/shared/constants/queues';
import { ProcessorsNamesEnumerator } from '../models/enumerators/processors-names.enumerator';
import MessageDTO from 'src/shared/dtos/message.dto';
import ActionEnumerator from 'src/shared/enumerators/action.enumerator';
import { GamesService } from 'src/games/games.service';
import { createGameDataDTO } from 'src/games/models/dtos/game-data.dto';

@Processor(queuesConst.scheduler)
export class SchedulerProcessor extends WorkerHost {
  constructor(private readonly gamesService: GamesService) {
    super();
  }

  async process(job: Job<MessageDTO<any>>) {
    switch (job.name) {
      case ProcessorsNamesEnumerator.gameEvents: {
        await this.processGameEvents(job.data);
        break;
      }
    }
  }

  async processGameEvents(info: MessageDTO<any>) {
    const gameData = createGameDataDTO({
      connectionId: info.connectionId,
      token: info.authorization,
      data: info.data,
    });
    switch (info.action) {
      case ActionEnumerator.cancelGame: {
        return await this.gamesService.cancelGame(gameData);
      }
      case ActionEnumerator.changeState: {
        return await this.gamesService.changeGameState(gameData);
      }
      case ActionEnumerator.addBot: {
        return await this.gamesService.addBot(gameData);
      }
    }
    return info;
  }
}
