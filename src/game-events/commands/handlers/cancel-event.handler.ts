import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { CancelEventCommand } from '../cancel-event.command';
import * as AWS from 'aws-sdk';
import showDebugConsole from 'src/shared/utils/show-debug-console';

@CommandHandler(CancelEventCommand)
export class CancelEventHandler implements ICommandHandler<CancelEventCommand> {
  async execute(command: CancelEventCommand): Promise<void> {
    const stepFunctions = new AWS.StepFunctions();

    try {
      const result = await stepFunctions
        .stopExecution({
          executionArn: command.eventId,
          cause: 'Execution canceled by system',
        })
        .promise();
    } catch (error) {
      throw error;
    }
  }
}
