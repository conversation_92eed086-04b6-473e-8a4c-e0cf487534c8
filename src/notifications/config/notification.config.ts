import { NotificationProvider } from '../enums/notification-provider.enum';

export interface NotificationConfig {
  defaultProvider: NotificationProvider;
  providers: {
    fcm?: {
      enabled: boolean;
      configPath?: string;
    };
    onesignal?: {
      enabled: boolean;
      appId?: string;
      apiKey?: string;
    };
    pusher?: {
      enabled: boolean;
      instanceId?: string;
      secretKey?: string;
    };
  };
}

export const getNotificationConfig = (): NotificationConfig => {
  return {
    defaultProvider: (process.env.PUSH_NOTIFICATION_PROVIDER as NotificationProvider) || NotificationProvider.FCM,
    providers: {
      fcm: {
        enabled: !!process.env.FIREBASE_CONF,
        configPath: process.env.FIREBASE_CONF,
      },
      onesignal: {
        enabled: !!(process.env.ONESIGNAL_APP_ID && process.env.ONESIGNAL_API_KEY),
        appId: process.env.ONESIGNAL_APP_ID,
        apiKey: process.env.ONESIGNAL_API_KEY,
      },
      pusher: {
        enabled: !!(process.env.PUSHER_INSTANCE_ID && process.env.PUSHER_SECRET_KEY),
        instanceId: process.env.PUSHER_INSTANCE_ID,
        secretKey: process.env.PUSHER_SECRET_KEY,
      },
    },
  };
};
