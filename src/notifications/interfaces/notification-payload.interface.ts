export interface NotificationPayload {
  title: string;
  body: string;
  data?: Record<string, any>;
  imageUrl?: string;
  sound?: string;
  badge?: number;
  clickAction?: string;
  icon?: string;
  tag?: string;
  color?: string;
  priority?: 'high' | 'normal';
  ttl?: number; // Time to live in seconds
}

export interface BulkNotificationPayload {
  tokens: string[];
  payload: NotificationPayload;
}

export interface NotificationResult {
  success: boolean;
  messageId?: string;
  error?: string;
  failedTokens?: string[];
}

export interface BulkNotificationResult {
  successCount: number;
  failureCount: number;
  results: NotificationResult[];
  failedTokens?: string[];
}
