import {
  NotificationPayload,
  BulkNotificationPayload,
  NotificationResult,
  BulkNotificationResult,
} from './notification-payload.interface';

export interface IPushNotificationProvider {
  /**
   * Send a single push notification
   * @param token Device token
   * @param payload Notification payload
   * @returns Promise with notification result
   */
  sendNotification(
    token: string,
    payload: NotificationPayload,
  ): Promise<NotificationResult>;

  /**
   * Send bulk push notifications
   * @param data Bulk notification data
   * @returns Promise with bulk notification result
   */
  sendBulkNotifications(
    data: BulkNotificationPayload,
  ): Promise<BulkNotificationResult>;

  /**
   * Send notifications to multiple tokens with the same payload
   * @param tokens Array of device tokens
   * @param payload Notification payload
   * @returns Promise with bulk notification result
   */
  sendToMultipleTokens(
    tokens: string[],
    payload: NotificationPayload,
  ): Promise<BulkNotificationResult>;

  /**
   * Validate if the provider is properly configured
   * @returns Promise<boolean>
   */
  isConfigured(): Promise<boolean>;

  /**
   * Get provider name
   * @returns string
   */
  getProviderName(): string;
}
