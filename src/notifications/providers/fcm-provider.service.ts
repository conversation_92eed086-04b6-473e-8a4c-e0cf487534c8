import { Injectable, Logger } from '@nestjs/common';
import * as admin from 'firebase-admin';
import { IPushNotificationProvider } from '../interfaces/push-notification.interface';
import {
  NotificationPayload,
  BulkNotificationPayload,
  NotificationResult,
  BulkNotificationResult,
} from '../interfaces/notification-payload.interface';
import showDebugConsole from '../../shared/utils/show-debug-console';
import bigIntReplacer from 'src/shared/utils/replacer';

@Injectable()
export class FcmProviderService implements IPushNotificationProvider {
  private readonly logger = new Logger(FcmProviderService.name);
  private isInitialized = false;

  constructor() {
    this.initializeFirebase();
  }

  private initializeFirebase(): void {
    try {
      if (!process.env.FIREBASE_CONF) {
        this.logger.warn('FIREBASE_CONF environment variable not found');
        return;
      }

      if (admin.apps.length === 0) {
        const parsedConf = process.env.FIREBASE_CONF.replace(/\n/g, '\n');
        const firebaseConfig = JSON.parse(parsedConf);

        admin.initializeApp({
          credential: admin.credential.cert(
            firebaseConfig as admin.ServiceAccount,
          ),
        });

        this.isInitialized = true;
        showDebugConsole('Firebase Admin SDK initialized successfully');
      } else {
        this.isInitialized = true;
        showDebugConsole('Firebase Admin SDK already initialized');
      }
    } catch (error) {
      this.logger.error('Failed to initialize Firebase Admin SDK', error);
      this.isInitialized = false;
    }
  }

  async sendNotification(
    token: string,
    payload: NotificationPayload,
  ): Promise<NotificationResult> {
    try {
      if (!this.isInitialized) {
        return {
          success: false,
          error: 'FCM provider not properly initialized',
        };
      }

      const message: admin.messaging.Message = {
        token,
        notification: {
          title: payload.title,
          body: payload.body,
          imageUrl: payload.imageUrl,
        },
        data: payload.data
          ? this.convertDataToStrings(payload.data)
          : undefined,
        android: {
          notification: {
            sound: payload.sound || 'default',
            priority: payload.priority === 'high' ? 'high' : 'default',
            color: payload.color,
            tag: payload.tag,
            icon: payload.icon,
            clickAction: payload.clickAction,
          },
          ttl: payload.ttl ? payload.ttl * 1000 : undefined, // Convert to milliseconds
        },
        apns: {
          payload: {
            aps: {
              sound: payload.sound || 'default',
              badge: payload.badge,
            },
          },
        },
        webpush: {
          notification: {
            icon: payload.icon,
            badge: payload.badge?.toString(),
            tag: payload.tag,
          },
        },
      };

      const response = await admin.messaging().send(message);

      showDebugConsole(`FCM notification sent successfully: ${response}`);

      return {
        success: true,
        messageId: response,
      };
    } catch (error) {
      this.logger.error('Failed to send FCM notification', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  async sendBulkNotifications(
    data: BulkNotificationPayload,
  ): Promise<BulkNotificationResult> {
    return this.sendToMultipleTokens(data.tokens, data.payload);
  }

  async sendToMultipleTokens(
    tokens: string[],
    payload: NotificationPayload,
  ): Promise<BulkNotificationResult> {
    try {
      if (!this.isInitialized) {
        return {
          successCount: 0,
          failureCount: tokens.length,
          results: tokens.map(() => ({
            success: false,
            error: 'FCM provider not properly initialized',
          })),
          failedTokens: tokens,
        };
      }

      if (tokens.length === 0) {
        return {
          successCount: 0,
          failureCount: 0,
          results: [],
        };
      }

      const message: admin.messaging.MulticastMessage = {
        tokens,
        notification: {
          title: payload.title,
          body: payload.body,
          imageUrl: payload.imageUrl,
        },
        data: payload.data
          ? this.convertDataToStrings(payload.data)
          : undefined,
        android: {
          notification: {
            sound: payload.sound || 'default',
            priority: payload.priority === 'high' ? 'high' : 'default',
            color: payload.color,
            tag: payload.tag,
            icon: payload.icon,
            clickAction: payload.clickAction,
          },
          ttl: payload.ttl ? payload.ttl * 1000 : undefined,
        },
        apns: {
          payload: {
            aps: {
              sound: payload.sound || 'default',
              badge: payload.badge,
            },
          },
        },
        webpush: {
          notification: {
            icon: payload.icon,
            badge: payload.badge?.toString(),
            tag: payload.tag,
          },
        },
      };

      const response = await admin.messaging().sendEachForMulticast(message);

      const results: NotificationResult[] = response.responses.map((resp) => ({
        success: resp.success,
        messageId: resp.messageId,
        error: resp.error?.message,
      }));

      const failedTokens = tokens.filter(
        (_, index) => !response.responses[index].success,
      );

      showDebugConsole(
        `FCM bulk notification sent. Success: ${response.successCount}, Failed: ${response.failureCount}`,
      );

      return {
        successCount: response.successCount,
        failureCount: response.failureCount,
        results,
        failedTokens,
      };
    } catch (error) {
      this.logger.error('Failed to send FCM bulk notifications', error);
      return {
        successCount: 0,
        failureCount: tokens.length,
        results: tokens.map(() => ({
          success: false,
          error: error.message,
        })),
        failedTokens: tokens,
      };
    }
  }

  async isConfigured(): Promise<boolean> {
    return this.isInitialized && !!process.env.FIREBASE_CONF;
  }

  getProviderName(): string {
    return 'FCM';
  }

  private convertDataToStrings(
    data: Record<string, any>,
  ): Record<string, string> {
    const result: Record<string, string> = {};
    for (const [key, value] of Object.entries(data)) {
      result[key] =
        typeof value === 'string'
          ? value
          : JSON.stringify(value, bigIntReplacer);
    }
    return result;
  }
}
