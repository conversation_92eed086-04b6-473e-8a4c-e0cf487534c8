/**
 * Exemplos de uso do módulo genérico de Push Notifications
 *
 * Este arquivo demonstra como usar o NotificationsService para enviar
 * notificações push de forma genérica, independente do provedor.
 */

import { Injectable } from '@nestjs/common';
import { NotificationsService } from '../notifications.service';
import { NotificationPayload } from '../interfaces/notification-payload.interface';

@Injectable()
export class NotificationExamplesService {
  constructor(private readonly notificationsService: NotificationsService) {}

  /**
   * Exemplo 1: Enviar notificação simples
   */
  async sendSimpleNotification(token: string): Promise<void> {
    const payload: NotificationPayload = {
      title: 'Tí<PERSON><PERSON> da Notificação',
      body: 'Corpo da mensagem da notificação',
    };

    const result = await this.notificationsService.sendNotification(
      token,
      payload,
    );

    if (result.success) {
      console.log('Notificação enviada com sucesso:', result.messageId);
    } else {
      console.error('Erro ao enviar notificação:', result.error);
    }
  }

  /**
   * Exemplo 2: Enviar notificação com dados customizados
   */
  async sendNotificationWithData(token: string): Promise<void> {
    const payload: NotificationPayload = {
      title: 'Nova mensagem',
      body: 'Você recebeu uma nova mensagem',
      data: {
        type: 'message',
        messageId: '12345',
        senderId: '67890',
        chatId: 'chat_abc123',
      },
      sound: 'default',
      priority: 'high',
      badge: 1,
    };

    const result = await this.notificationsService.sendNotification(
      token,
      payload,
    );
    console.log('Resultado:', result);
  }

  /**
   * Exemplo 3: Enviar notificação para múltiplos tokens
   */
  async sendToMultipleUsers(tokens: string[]): Promise<void> {
    const payload: NotificationPayload = {
      title: 'Notificação em massa',
      body: 'Esta é uma notificação enviada para múltiplos usuários',
      data: {
        type: 'broadcast',
        timestamp: new Date().toISOString(),
      },
      priority: 'normal',
    };

    const result = await this.notificationsService.sendToMultipleTokens(
      tokens,
      payload,
    );

    console.log(
      `Enviadas: ${result.successCount}, Falharam: ${result.failureCount}`,
    );

    if (result.failedTokens && result.failedTokens.length > 0) {
      console.log('Tokens que falharam:', result.failedTokens);
    }
  }

  /**
   * Exemplo 4: Enviar notificação de jogo
   */
  async sendGameNotification(token: string, gameData: any): Promise<void> {
    const payload: NotificationPayload = {
      title: 'Sua vez de jogar!',
      body: `É sua vez no jogo ${gameData.gameName}`,
      data: {
        type: 'game_turn',
        gameId: gameData.gameId,
        roomId: gameData.roomId,
        playerId: gameData.playerId,
      },
      sound: 'game_sound.wav',
      priority: 'high',
      badge: 1,
      clickAction: 'GAME_ACTIVITY',
    };

    await this.notificationsService.sendNotification(token, payload);
  }

  /**
   * Exemplo 5: Verificar status do provedor
   */
  async checkProviderStatus(): Promise<void> {
    const isConfigured = await this.notificationsService.isConfigured();
    const currentProvider = this.notificationsService.getCurrentProvider();
    const availableProviders =
      this.notificationsService.getAvailableProviders();

    console.log('Provedor configurado:', isConfigured);
    console.log('Provedor atual:', currentProvider);
    console.log('Provedores disponíveis:', availableProviders);
  }

  /**
   * Exemplo 6: Trocar provedor em tempo de execução
   */
  async switchProvider(): Promise<void> {
    // Exemplo de como trocar para OneSignal (quando implementado)
    // const switched = await this.notificationsService.switchProvider(NotificationProvider.ONESIGNAL);
    // console.log('Provedor trocado:', switched);
  }

  /**
   * Exemplo 7: Notificação com imagem
   */
  async sendNotificationWithImage(token: string): Promise<void> {
    const payload: NotificationPayload = {
      title: 'Nova foto compartilhada',
      body: 'João compartilhou uma nova foto',
      imageUrl: 'https://example.com/image.jpg',
      data: {
        type: 'photo_share',
        photoId: 'photo_123',
        userId: 'user_456',
      },
      priority: 'normal',
    };

    await this.notificationsService.sendNotification(token, payload);
  }

  /**
   * Exemplo 8: Notificação com configurações avançadas
   */
  async sendAdvancedNotification(token: string): Promise<void> {
    const payload: NotificationPayload = {
      title: 'Notificação Avançada',
      body: 'Esta notificação tem configurações avançadas',
      data: {
        type: 'advanced',
        customData: JSON.stringify({ key: 'value' }),
      },
      sound: 'custom_sound.wav',
      priority: 'high',
      badge: 5,
      color: '#FF0000',
      tag: 'advanced_notification',
      icon: 'ic_notification',
      ttl: 3600, // 1 hora
      clickAction: 'ADVANCED_ACTIVITY',
    };

    const result = await this.notificationsService.sendNotification(
      token,
      payload,
    );

    if (!result.success) {
      console.error('Falha ao enviar notificação avançada:', result.error);
    }
  }
}
