import { Injectable, Logger, Inject } from '@nestjs/common';
import { IPushNotificationProvider } from './interfaces/push-notification.interface';
import {
  NotificationPayload,
  BulkNotificationPayload,
  NotificationResult,
  BulkNotificationResult,
} from './interfaces/notification-payload.interface';
import { NotificationProvider } from './enums/notification-provider.enum';

@Injectable()
export class NotificationsService {
  private readonly logger = new Logger(NotificationsService.name);
  private currentProvider: IPushNotificationProvider;

  constructor(
    @Inject('PUSH_NOTIFICATION_PROVIDERS')
    private readonly providers: Map<
      NotificationProvider,
      IPushNotificationProvider
    >,
  ) {
    this.initializeProvider();
  }

  private async initializeProvider(): Promise<void> {
    // Default to FCM, but can be configured via environment variable
    const providerType =
      (process.env.PUSH_NOTIFICATION_PROVIDER as NotificationProvider) ||
      NotificationProvider.FCM;

    const provider = this.providers.get(providerType);
    if (!provider) {
      this.logger.error(`Push notification provider ${providerType} not found`);
      throw new Error(`Push notification provider ${providerType} not found`);
    }

    const isConfigured = await provider.isConfigured();
    if (!isConfigured) {
      this.logger.warn(
        `Push notification provider ${providerType} is not properly configured`,
      );
    }

    this.currentProvider = provider;
    this.logger.log(
      `Using push notification provider: ${provider.getProviderName()}`,
    );
  }

  /**
   * Send a single push notification
   */
  async sendNotification(
    token: string,
    payload: NotificationPayload,
  ): Promise<NotificationResult> {
    if (!this.currentProvider) {
      return {
        success: false,
        error: 'No push notification provider configured',
      };
    }

    return this.currentProvider.sendNotification(token, payload);
  }

  /**
   * Send bulk push notifications
   */
  async sendBulkNotifications(
    data: BulkNotificationPayload,
  ): Promise<BulkNotificationResult> {
    if (!this.currentProvider) {
      return {
        successCount: 0,
        failureCount: data.tokens.length,
        results: data.tokens.map(() => ({
          success: false,
          error: 'No push notification provider configured',
        })),
        failedTokens: data.tokens,
      };
    }

    return this.currentProvider.sendBulkNotifications(data);
  }

  /**
   * Send notifications to multiple tokens with the same payload
   */
  async sendToMultipleTokens(
    tokens: string[],
    payload: NotificationPayload,
  ): Promise<BulkNotificationResult> {
    if (!this.currentProvider) {
      return {
        successCount: 0,
        failureCount: tokens.length,
        results: tokens.map(() => ({
          success: false,
          error: 'No push notification provider configured',
        })),
        failedTokens: tokens,
      };
    }

    return this.currentProvider.sendToMultipleTokens(tokens, payload);
  }

  /**
   * Switch to a different provider at runtime
   */
  async switchProvider(providerType: NotificationProvider): Promise<boolean> {
    const provider = this.providers.get(providerType);
    if (!provider) {
      this.logger.error(`Push notification provider ${providerType} not found`);
      return false;
    }

    const isConfigured = await provider.isConfigured();
    if (!isConfigured) {
      this.logger.warn(
        `Push notification provider ${providerType} is not properly configured`,
      );
      return false;
    }

    this.currentProvider = provider;
    this.logger.log(
      `Switched to push notification provider: ${provider.getProviderName()}`,
    );
    return true;
  }

  /**
   * Get current provider information
   */
  getCurrentProvider(): { name: string; configured: boolean } {
    if (!this.currentProvider) {
      return { name: 'None', configured: false };
    }

    return {
      name: this.currentProvider.getProviderName(),
      configured: true,
    };
  }

  /**
   * Get all available providers
   */
  getAvailableProviders(): string[] {
    return Array.from(this.providers.keys());
  }

  /**
   * Check if current provider is configured
   */
  async isConfigured(): Promise<boolean> {
    if (!this.currentProvider) {
      return false;
    }

    return this.currentProvider.isConfigured();
  }
}
