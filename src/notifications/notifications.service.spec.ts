import { Test, TestingModule } from '@nestjs/testing';
import { NotificationsService } from './notifications.service';
import { FcmProviderService } from './providers/fcm-provider.service';
import { NotificationProvider } from './enums/notification-provider.enum';

describe('NotificationsService', () => {
  let service: NotificationsService;
  let fcmProvider: FcmProviderService;

  beforeEach(async () => {
    const mockFcmProvider = {
      sendNotification: jest.fn(),
      sendBulkNotifications: jest.fn(),
      sendToMultipleTokens: jest.fn(),
      isConfigured: jest.fn().mockResolvedValue(true),
      getProviderName: jest.fn().mockReturnValue('FCM'),
    };

    const providers = new Map();
    providers.set(NotificationProvider.FCM, mockFcmProvider);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationsService,
        {
          provide: 'PUSH_NOTIFICATION_PROVIDERS',
          useValue: providers,
        },
      ],
    }).compile();

    service = module.get<NotificationsService>(NotificationsService);
    fcmProvider = providers.get(NotificationProvider.FCM);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should send notification using current provider', async () => {
    const mockResult = { success: true, messageId: 'test-message-id' };
    fcmProvider.sendNotification = jest.fn().mockResolvedValue(mockResult);

    const result = await service.sendNotification('test-token', {
      title: 'Test',
      body: 'Test message',
    });

    expect(result).toEqual(mockResult);
    expect(fcmProvider.sendNotification).toHaveBeenCalledWith('test-token', {
      title: 'Test',
      body: 'Test message',
    });
  });

  it('should send to multiple tokens', async () => {
    const mockResult = { successCount: 2, failureCount: 0, results: [] };
    fcmProvider.sendToMultipleTokens = jest.fn().mockResolvedValue(mockResult);

    const tokens = ['token1', 'token2'];
    const payload = { title: 'Test', body: 'Test message' };

    const result = await service.sendToMultipleTokens(tokens, payload);

    expect(result).toEqual(mockResult);
    expect(fcmProvider.sendToMultipleTokens).toHaveBeenCalledWith(tokens, payload);
  });

  it('should return current provider info', () => {
    const providerInfo = service.getCurrentProvider();
    expect(providerInfo.name).toBe('FCM');
    expect(providerInfo.configured).toBe(true);
  });

  it('should check if configured', async () => {
    const isConfigured = await service.isConfigured();
    expect(isConfigured).toBe(true);
    expect(fcmProvider.isConfigured).toHaveBeenCalled();
  });
});
