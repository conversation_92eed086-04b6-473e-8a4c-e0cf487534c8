import { Modu<PERSON> } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { FcmProviderService } from './providers/fcm-provider.service';
import { IPushNotificationProvider } from './interfaces/push-notification.interface';
import { NotificationProvider } from './enums/notification-provider.enum';

@Module({
  providers: [
    NotificationsService,
    FcmProviderService,
    {
      provide: 'PUSH_NOTIFICATION_PROVIDERS',
      useFactory: (fcmProvider: FcmProviderService) => {
        const providers = new Map<NotificationProvider, IPushNotificationProvider>();
        providers.set(NotificationProvider.FCM, fcmProvider);
        // Add other providers here as they are implemented
        // providers.set(NotificationProvider.ONESIGNAL, oneSignalProvider);
        // providers.set(NotificationProvider.PUSHER, pusherProvider);
        return providers;
      },
      inject: [FcmProviderService],
    },
  ],
  exports: [NotificationsService],
})
export class NotificationsModule {}
