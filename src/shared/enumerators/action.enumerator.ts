enum ActionEnumerator {
  // inbound
  connect = 'connect',
  initiate = 'initiate',
  startGame = 'startGame',
  cancelGame = 'cancelGame',
  addBot = 'addBot',
  addBots = 'addBots',
  disconnectEndGame = 'disconnecTEndGame',
  setPlayerAnswer = 'setPlayerAnswer',
  setPlayerPontuation = 'setPlayerPontuation',
  disconnect = 'disconnect',
  // inbound from external tools
  changeState = 'changeState',
  // outbound
  welcome = 'welcome',
  gameStarting = 'gameStarting',
  failure = 'failure',
  userEntered = 'userEntered',
  userLeft = 'userLeft',
  countDown = 'countDown',
  isCompleted = 'isCompleted',
  gameCancelled = 'gameCancelled',
  startRound = 'startRound',
  updateAnswers = 'updateAnswers',
  updatePontuations = 'updatePontuations',
  playerReceivePontuation = 'playerReceivePontuation',
  reviewRound = 'reviewRound',
  endGame = 'endGame',
  endGameUpdateData = 'endGameUpdateData',
}

export default ActionEnumerator;
